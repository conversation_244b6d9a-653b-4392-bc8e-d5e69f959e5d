import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:neorevv/src/data/repository/auth_data_repository.dart';
import '../../domain/repository/get_access_token_repository.dart';

class DioInterceptor extends Interceptor {
  final GetAccessTokenRepository _getAccessTokenRepository;

  DioInterceptor(this._getAccessTokenRepository);

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      try {
        // Call refresh token endpoint (no refresh token needed since using cookies)
        final newToken = await _getAccessTokenRepository.getAccessToken('');

        if (newToken != null) {
          // Save tokens to AuthDataRepository
          final authRepository = AuthDataRepository();
          authRepository.setTokens(newToken.jwt, newToken.refreshToken);

          final RequestOptions options = err.requestOptions;
          options.headers['Authorization'] = 'Bearer ${newToken.jwt}';

          final Dio dio = Dio();
          final response = await dio.fetch(options);

          return handler.resolve(response);
        }
      } catch (e) {
        debugPrint('Token refresh failed: $e');
      }
    }

    return handler.next(err);
  }
}
