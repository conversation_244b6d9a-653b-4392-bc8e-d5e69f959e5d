import 'package:get_it/get_it.dart';
import '/src/data/repository/filter_repository_impl.dart';
import 'package:neorevv/src/data/repository/get_infocard_repository_impl.dart';
import '/src/domain/repository/filter_repository.dart';
import 'package:neorevv/src/data/repository/get_network_item_repository_impl.dart';
import 'package:neorevv/src/domain/repository/get_network_item_repository.dart';
import '../../data/repository/auth_repository_impl.dart';
import '../../data/repository/broker_register_repository_impl.dart';
import '../../data/repository/filter_repository_impl.dart';
import '../../data/repository/sales_details_impl.dart';
import '../../data/repository/broker_repository_impl.dart';
import '../../data/repository/user_repository_impl.dart';
import '../../data/repository/agent_repository_impl.dart';
import '../../domain/repository/auth_repository.dart';
import '../../domain/repository/broker_register_repository.dart';
import '../../domain/repository/broker_repository.dart';
import '../../domain/repository/filter_repository.dart';
import '../../domain/repository/get_infocard_repository.dart';
import '../../domain/repository/sales_details_repository.dart';
import '../../domain/repository/user_repository.dart';
import '../../domain/repository/agent_repository.dart';
import '../../data/repository/top_performers_impl.dart';
import '../../domain/repository/top_performers_repository.dart';

import 'token_storage.dart';

final locator = GetIt.instance;

Future<void> initializeDependencies() async {
  // Register token storage
  locator.registerSingleton<TokenStorage>(TokenStorage.instance);

  // Register repositories
  locator.registerSingleton<AuthRepository>(AuthRepositoryImpl());
  locator.registerSingleton<BrokerRegisterRepository>(
    BrokerRegisterRepositoryImpl(),
  );

  // Register user repository factory
  locator.registerFactory<UserRepository>(() {
    return UserRepositoryImpl();
  });

  locator.registerFactory<BrokerRepository>(() {
    return BrokerRepositoryImpl();
  });

  // Register infocard repository factory
  locator.registerFactory<GetInfocardRepository>(() {
    return GetInfocardRepositoryImpl();
  });
  

  locator.registerSingleton<SalesDetailsRepository>(SalesDetailsRepositoryImpl());

  // Register agent repository factory
  locator.registerFactory<AgentRepository>(() {
    return AgentRepositoryImpl();
  });

  // Register top performers repository factory
  locator.registerFactory<TopPerformersRepository>(() {
    return TopPerformersRepositoryImpl();
  });

  // Register filter repository factory
  locator.registerFactory<FilterRepository>(() {
    return FilterRepositoryImpl();
  });
  locator.registerFactory<GetNetworkItemRepository>(() {
    return GetNetworkItemRepositoryImpl();
  });
}
